"""
HubStudio + YouTube 自动化上传 - 可视化界面
提供图形化的视频上传管理界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import configparser
from pathlib import Path
import queue
import time
from datetime import datetime

# 导入自动化模块
from main import HubStudioYouTubeAutomation
from video_uploader_unified import VideoUploaderUnified


class HubStudioGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("HubStudio + YouTube 自动化上传工具")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 设置窗口背景色为白色
        self.root.configure(bg='#FFFFFF')
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化变量
        self.config = configparser.ConfigParser()
        self.load_config()
        self.automation = None
        self.video_uploader = None  # 将在需要时初始化
        self.video_list = []  # 存储视频文件路径列表
        self.is_uploading = False
        self.log_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        self.setup_logging()
        
        # 启动日志更新线程
        self.update_logs()

        # 自动刷新环境列表
        self.root.after(1000, self.refresh_environments)
        

        
    def setup_styles(self):
        """设置现代化界面样式 - 白底蓝线主题"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 定义颜色方案
        colors = {
            'primary_blue': '#2E86C1',      # 主蓝色
            'light_blue': '#AED6F1',        # 浅蓝色
            'dark_blue': '#1B4F72',         # 深蓝色
            'white': '#FFFFFF',             # 白色
            'light_gray': '#F8F9FA',        # 浅灰色
            'border_gray': '#E5E5E5',       # 边框灰色
            'text_dark': '#2C3E50',         # 深色文字
            'success_green': '#27AE60',     # 成功绿色
            'error_red': '#E74C3C',         # 错误红色
            'warning_orange': '#F39C12'     # 警告橙色
        }
        
        # 配置主要组件样式
        style.configure('TFrame', background=colors['white'])
        style.configure('TLabelFrame', background=colors['white'], 
                       borderwidth=2, relief='solid', bordercolor=colors['primary_blue'])
        style.configure('TLabelFrame.Label', background=colors['white'], 
                       foreground=colors['primary_blue'], font=('Segoe UI', 11, 'bold'))
        
        # 按钮样式
        style.configure('TButton', 
                       background=colors['primary_blue'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 10),
                       padding=(15, 8))
        style.map('TButton',
                 background=[('active', colors['dark_blue']),
                           ('pressed', colors['dark_blue'])])
        
        # 输入框样式
        style.configure('TEntry',
                       fieldbackground=colors['white'],
                       borderwidth=2,
                       bordercolor=colors['border_gray'],
                       focuscolor=colors['primary_blue'],
                       font=('Segoe UI', 10),
                       padding=8)
        
        # 标签样式
        style.configure('TLabel', 
                       background=colors['white'],
                       foreground=colors['text_dark'],
                       font=('Segoe UI', 10))
        
        # 自定义样式
        style.configure('Title.TLabel', 
                       background=colors['white'],
                       foreground=colors['primary_blue'],
                       font=('Segoe UI', 18, 'bold'))
        
        style.configure('Header.TLabel', 
                       background=colors['white'],
                       foreground=colors['primary_blue'],
                       font=('Segoe UI', 12, 'bold'))
        
        style.configure('Subheader.TLabel', 
                       background=colors['white'],
                       foreground=colors['text_dark'],
                       font=('Segoe UI', 11, 'bold'))
        
        style.configure('Success.TLabel', 
                       background=colors['white'],
                       foreground=colors['success_green'],
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Error.TLabel', 
                       background=colors['white'],
                       foreground=colors['error_red'],
                       font=('Segoe UI', 10, 'bold'))
        
        style.configure('Warning.TLabel', 
                       background=colors['white'],
                       foreground=colors['warning_orange'],
                       font=('Segoe UI', 10, 'bold'))
        
        # 进度条样式
        style.configure('TProgressbar',
                       background=colors['primary_blue'],
                       troughcolor=colors['light_gray'],
                       borderwidth=0,
                       lightcolor=colors['primary_blue'],
                       darkcolor=colors['primary_blue'])
        
        # Spinbox样式
        style.configure('TSpinbox',
                       fieldbackground=colors['white'],
                       borderwidth=2,
                       bordercolor=colors['border_gray'],
                       font=('Segoe UI', 10))
        
        # Radiobutton样式
        style.configure('TRadiobutton',
                       background=colors['white'],
                       foreground=colors['text_dark'],
                       font=('Segoe UI', 10),
                       focuscolor='none')
        
        # 存储颜色方案供其他方法使用
        self.colors = colors
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists('config.ini'):
                self.config.read('config.ini', encoding='utf-8')
                # 检查配置完整性，如果缺少必要配置则补充
                config_updated = False
                
                if not self.config.has_section('BROWSER'):
                    self.config.add_section('BROWSER')
                    self.config.set('BROWSER', 'api_url', 'http://localhost:6873')
                    self.config.set('BROWSER', 'readonly_mode', 'false')
                    self.config.set('BROWSER', 'headless_mode', 'false')
                    self.config.set('BROWSER', 'cdp_hide', 'true')
                    config_updated = True
                    
                if not self.config.has_section('HUBSTUDIO'):
                    self.config.add_section('HUBSTUDIO')
                    self.config.set('HUBSTUDIO', 'api_id', '')
                    self.config.set('HUBSTUDIO', 'api_secret', '')
                    config_updated = True
                    
                if not self.config.has_section('VIDEO'):
                    self.config.add_section('VIDEO')
                    self.config.set('VIDEO', 'video_folder', './videos')
                    self.config.set('VIDEO', 'default_title', '自动上传视频')
                    self.config.set('VIDEO', 'default_description', '通过HubStudio自动化脚本上传到YouTube的视频')
                    self.config.set('VIDEO', 'default_tags', '自动化,HubStudio,YouTube,视频上传')
                    config_updated = True
                    
                if not self.config.has_section('AUTOMATION'):
                    self.config.add_section('AUTOMATION')
                    self.config.set('AUTOMATION', 'wait_timeout', '30')
                    self.config.set('AUTOMATION', 'upload_timeout', '600')
                    self.config.set('AUTOMATION', 'retry_count', '3')
                    self.config.set('AUTOMATION', 'upload_interval', '10')
                    config_updated = True
                    
                if not self.config.has_section('YOUTUBE'):
                    self.config.add_section('YOUTUBE')
                    self.config.set('YOUTUBE', 'upload_url', 'https://studio.youtube.com')
                    config_updated = True
                    
                # 如果配置有更新，保存到文件
                if config_updated:
                    self.save_config()
            else:
                self.create_default_config()
        except Exception as e:
            messagebox.showerror("配置错误", f"加载配置文件失败: {e}")
            
    def create_default_config(self):
        """创建默认配置"""
        self.config['BROWSER'] = {
            'api_url': 'http://localhost:6873',
            'readonly_mode': 'false',
            'headless_mode': 'false',
            'cdp_hide': 'true'
        }
        self.config['HUBSTUDIO'] = {
            'api_id': '',
            'api_secret': ''
        }
        self.config['YOUTUBE'] = {
            'upload_url': 'https://studio.youtube.com'
        }
        self.config['VIDEO'] = {
            'video_folder': './videos',
            'default_title': '自动上传视频',
            'default_description': '通过HubStudio自动化脚本上传到YouTube的视频',
            'default_tags': '自动化,HubStudio,YouTube,视频上传'
        }
        self.config['AUTOMATION'] = {
            'wait_timeout': '30',
            'upload_timeout': '600',
            'retry_count': '3',
            'upload_interval': '10'
        }
        self.save_config()
        
    def save_config(self):
        """保存配置文件"""
        try:
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            messagebox.showerror("保存错误", f"保存配置文件失败: {e}")
            
    def create_widgets(self):
        """创建现代化界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 标题栏
        main_frame.rowconfigure(1, weight=0)  # 视频选择栏
        main_frame.rowconfigure(2, weight=1)  # 主要内容区域
        main_frame.rowconfigure(3, weight=0)  # 日志区域
        main_frame.rowconfigure(4, weight=0)  # 状态栏
        
        # 添加标题栏
        self.create_title_bar(main_frame)
        
        # 顶部视频选择栏
        self.create_video_selection_bar(main_frame)
        
        # 中间主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=15)
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧面板 - 配置和控制
        self.create_control_panel(content_frame)
        
        # 右侧面板 - 设置
        self.create_settings_panel(content_frame)
        
        # 底部实时日志
        self.create_log_panel(main_frame)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
    
    def create_title_bar(self, parent):
        """创建标题栏"""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(0, weight=1)
        
        # 主标题
        title_label = ttk.Label(title_frame, text="🚀 HubStudio + YouTube 自动化上传工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # 副标题
        subtitle_label = ttk.Label(title_frame, text="高效、智能、批量上传视频到YouTube平台", style='Subheader.TLabel')
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # 分隔线
        separator = ttk.Separator(title_frame, orient='horizontal')
        separator.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        
    def create_video_selection_bar(self, parent):
        """创建现代化视频选择栏"""
        video_frame = ttk.LabelFrame(parent, text="📹 视频文件选择", padding="15")
        video_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        video_frame.columnconfigure(2, weight=1)
        
        # 选择视频按钮
        select_btn = ttk.Button(video_frame, text="📁 选择视频文件", command=self.add_video_files)
        select_btn.grid(row=0, column=0, padx=(0, 15))
        
        # 视频状态显示
        self.video_count_var = tk.StringVar(value="未选择视频")
        status_label = ttk.Label(video_frame, textvariable=self.video_count_var, style='Subheader.TLabel')
        status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 15))
        
        # 视频文件路径显示框
        self.video_path_var = tk.StringVar(value="")
        self.video_path_entry = ttk.Entry(video_frame, textvariable=self.video_path_var, state='readonly', width=60)
        self.video_path_entry.grid(row=0, column=2, sticky=(tk.W, tk.E), padx=(0, 15))
        
        # 操作按钮组
        button_frame = ttk.Frame(video_frame)
        button_frame.grid(row=0, column=3)
        
        # 清除视频按钮
        clear_btn = ttk.Button(button_frame, text="🗑️ 清除", command=self.clear_video_selection)
        clear_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 开始上传按钮
        self.upload_btn = ttk.Button(button_frame, text="🚀 开始上传", command=self.start_upload, state='disabled')
        self.upload_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 停止上传按钮
        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止上传", command=self.stop_upload, state='disabled')
        self.stop_btn.grid(row=0, column=2)
    
    def create_control_panel(self, parent):
        """创建现代化控制面板"""
        control_frame = ttk.LabelFrame(parent, text="⚙️ 视频配置", padding="20")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        control_frame.columnconfigure(0, weight=1)
        
        # HubStudio状态显示区域
        status_frame = ttk.LabelFrame(control_frame, text="🔗 连接状态", padding="15")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        status_frame.columnconfigure(0, weight=1)
        
        self.hubstudio_status_var = tk.StringVar(value="正在检测HubStudio环境...")
        self.status_display = ttk.Label(status_frame, textvariable=self.hubstudio_status_var, style='Warning.TLabel')
        self.status_display.grid(row=0, column=0, sticky=tk.W)

        # 并发设置框架
        concurrent_frame = ttk.LabelFrame(control_frame, text="⚡ 并发设置", padding="15")
        concurrent_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        concurrent_frame.columnconfigure(1, weight=1)
        
        # 并发数量设置
        ttk.Label(concurrent_frame, text="并发数量:", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        spinbox_frame = ttk.Frame(concurrent_frame)
        spinbox_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.concurrent_count_var = tk.IntVar(value=1)
        concurrent_spinbox = ttk.Spinbox(spinbox_frame, from_=1, to=10, textvariable=self.concurrent_count_var, width=8)
        concurrent_spinbox.grid(row=0, column=0, padx=(0, 10))
        
        ttk.Label(spinbox_frame, text="个浏览器环境同时工作").grid(row=0, column=1, sticky=tk.W)
        
        # 说明文字
        ttk.Label(concurrent_frame, text="💡 系统将自动使用所有可用的HubStudio环境", 
                 foreground=self.colors['text_dark'], font=('Segoe UI', 9)).grid(row=2, column=0, sticky=tk.W)

        # 视频信息框架
        video_info_frame = ttk.LabelFrame(control_frame, text="📝 视频信息", padding="15")
        video_info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        video_info_frame.columnconfigure(0, weight=1)
        video_info_frame.rowconfigure(3, weight=1)
        
        # 视频标题
        ttk.Label(video_info_frame, text="标题:", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 8))
        self.title_var = tk.StringVar(value="")
        title_entry = ttk.Entry(video_info_frame, textvariable=self.title_var, font=('Segoe UI', 11))
        title_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 视频描述
        ttk.Label(video_info_frame, text="描述:", style='Subheader.TLabel').grid(row=2, column=0, sticky=tk.W, pady=(0, 8))
        
        # 创建描述文本框和滚动条
        desc_frame = ttk.Frame(video_info_frame)
        desc_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        desc_frame.columnconfigure(0, weight=1)
        desc_frame.rowconfigure(0, weight=1)
        
        self.desc_text = tk.Text(desc_frame, height=6, wrap=tk.WORD, font=('Segoe UI', 10),
                                bg=self.colors['white'], fg=self.colors['text_dark'],
                                borderwidth=2, relief='solid')
        self.desc_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        desc_scrollbar = ttk.Scrollbar(desc_frame, orient='vertical', command=self.desc_text.yview)
        desc_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.desc_text.configure(yscrollcommand=desc_scrollbar.set)

        # 儿童内容设置
        children_frame = ttk.LabelFrame(video_info_frame, text="👶 内容分级", padding="10")
        children_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        
        self.children_content_var = tk.StringVar(value="否")
        radio_frame = ttk.Frame(children_frame)
        radio_frame.grid(row=0, column=0, sticky=tk.W)
        
        ttk.Radiobutton(radio_frame, text="✅ 适合儿童", variable=self.children_content_var, value="是").pack(side=tk.LEFT, padx=(0, 30))
        ttk.Radiobutton(radio_frame, text="❌ 不适合儿童", variable=self.children_content_var, value="否").pack(side=tk.LEFT)
    
    def create_settings_panel(self, parent):
        """创建带滚动条的现代化设置面板"""
        # 主设置框架
        settings_main_frame = ttk.LabelFrame(parent, text="🔧 HubStudio 设置", padding="10")
        settings_main_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(15, 0))
        settings_main_frame.columnconfigure(0, weight=1)
        settings_main_frame.rowconfigure(0, weight=1)
        
        # 创建Canvas和Scrollbar容器
        canvas_frame = ttk.Frame(settings_main_frame)
        canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)
        
        # 创建Canvas
        self.settings_canvas = tk.Canvas(canvas_frame, 
                                        bg=self.colors['white'],
                                        highlightthickness=0,
                                        borderwidth=0)
        self.settings_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建垂直滚动条
        settings_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.settings_canvas.yview)
        settings_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.settings_canvas.configure(yscrollcommand=settings_scrollbar.set)
        
        # 创建可滚动的内容框架
        self.settings_scrollable_frame = ttk.Frame(self.settings_canvas)
        self.settings_canvas_window = self.settings_canvas.create_window((0, 0), window=self.settings_scrollable_frame, anchor="nw")
        
        # 绑定Canvas大小变化事件
        self.settings_scrollable_frame.bind("<Configure>", self._on_settings_frame_configure)
        self.settings_canvas.bind("<Configure>", self._on_settings_canvas_configure)
        
        # 绑定鼠标滚轮事件
        self.settings_canvas.bind("<MouseWheel>", self._on_settings_mousewheel)
        
        # 在可滚动框架中创建实际内容
        self._create_settings_content(self.settings_scrollable_frame)
    
    def _on_settings_frame_configure(self, event):
        """当内容框架大小改变时更新滚动区域"""
        self.settings_canvas.configure(scrollregion=self.settings_canvas.bbox("all"))
    
    def _on_settings_canvas_configure(self, event):
        """当Canvas大小改变时调整内容框架宽度"""
        canvas_width = event.width
        self.settings_canvas.itemconfig(self.settings_canvas_window, width=canvas_width)
    
    def _on_settings_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.settings_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def _create_settings_content(self, parent):
        """创建设置面板的实际内容"""
        parent.columnconfigure(0, weight=1)
        
        # API状态显示区域
        status_frame = ttk.LabelFrame(parent, text="📡 API 连接状态", padding="15")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        status_frame.columnconfigure(0, weight=1)
        
        # 状态显示行
        status_row = ttk.Frame(status_frame)
        status_row.grid(row=0, column=0, sticky=(tk.W, tk.E))
        status_row.columnconfigure(1, weight=1)
        
        ttk.Label(status_row, text="连接状态:", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.api_status_var = tk.StringVar(value="🔴 未连接")
        self.api_status_label = ttk.Label(status_row, textvariable=self.api_status_var, font=('Segoe UI', 10, 'bold'))
        self.api_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # API配置区域
        config_frame = ttk.LabelFrame(parent, text="🔑 API 配置", padding="15")
        config_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(0, weight=1)
        
        # API地址配置
        api_url_frame = ttk.Frame(config_frame)
        api_url_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        api_url_frame.columnconfigure(1, weight=1)
        
        ttk.Label(api_url_frame, text="API 地址:", style='Subheader.TLabel', width=12).grid(row=0, column=0, sticky=tk.W)
        self.api_url_var = tk.StringVar(value=self.config.get('BROWSER', 'api_url', fallback='http://localhost:6873'))
        api_url_entry = ttk.Entry(api_url_frame, textvariable=self.api_url_var, font=('Segoe UI', 10))
        api_url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # API ID配置
        api_id_frame = ttk.Frame(config_frame)
        api_id_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        api_id_frame.columnconfigure(1, weight=1)
        
        ttk.Label(api_id_frame, text="API ID:", style='Subheader.TLabel', width=12).grid(row=0, column=0, sticky=tk.W)
        self.api_id_var = tk.StringVar(value=self.config.get('HUBSTUDIO', 'api_id', fallback=''))
        api_id_entry = ttk.Entry(api_id_frame, textvariable=self.api_id_var, font=('Segoe UI', 10))
        api_id_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # API Secret配置
        api_secret_frame = ttk.Frame(config_frame)
        api_secret_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        api_secret_frame.columnconfigure(1, weight=1)
        
        ttk.Label(api_secret_frame, text="API Secret:", style='Subheader.TLabel', width=12).grid(row=0, column=0, sticky=tk.W)
        self.api_secret_var = tk.StringVar(value=self.config.get('HUBSTUDIO', 'api_secret', fallback=''))
        api_secret_entry = ttk.Entry(api_secret_frame, textvariable=self.api_secret_var, font=('Segoe UI', 10), show="*")
        api_secret_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 配置说明
        info_frame = ttk.Frame(config_frame)
        info_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        info_label = ttk.Label(info_frame, 
                              text="💡 配置说明:\n• 请在HubStudio中获取API配置信息\n• 确保API服务已启用并配置正确\n• API地址通常为 http://localhost:6873",
                              foreground=self.colors['text_dark'], 
                              font=('Segoe UI', 9),
                              justify='left')
        info_label.grid(row=0, column=0, sticky=tk.W)
        
        # 环境信息区域
        env_frame = ttk.LabelFrame(parent, text="🌐 环境信息", padding="15")
        env_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        env_frame.columnconfigure(0, weight=1)
        
        # 环境数量显示
        env_count_frame = ttk.Frame(env_frame)
        env_count_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        env_count_frame.columnconfigure(1, weight=1)
        
        ttk.Label(env_count_frame, text="可用环境:", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.env_count_var = tk.StringVar(value="0 个")
        env_count_label = ttk.Label(env_count_frame, textvariable=self.env_count_var, font=('Segoe UI', 10, 'bold'))
        env_count_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 环境列表显示
        env_list_label = ttk.Label(env_frame, text="环境列表:", style='Subheader.TLabel')
        env_list_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        self.env_list_text = tk.Text(env_frame, 
                                    height=4, 
                                    wrap=tk.WORD,
                                    font=('Consolas', 9),
                                    bg=self.colors['light_gray'],
                                    fg=self.colors['text_dark'],
                                    borderwidth=2,
                                    relief='solid',
                                    state='disabled')
        self.env_list_text.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 操作按钮区域
        action_frame = ttk.LabelFrame(parent, text="🎯 操作中心", padding="15")
        action_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)
        
        # 第一行按钮
        save_btn = ttk.Button(action_frame, text="💾 保存配置", command=self.save_current_config)
        save_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(0, 10))
        
        test_btn = ttk.Button(action_frame, text="🔍 测试连接", command=self.test_hubstudio_connection)
        test_btn.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 10))
        
        # 第二行按钮
        refresh_btn = ttk.Button(action_frame, text="🔄 刷新环境", command=self.refresh_environments)
        refresh_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(0, 10))
        
        help_btn = ttk.Button(action_frame, text="❓ 使用帮助", command=self.show_help)
        help_btn.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 10))
        
        # 高级设置区域
        advanced_frame = ttk.LabelFrame(parent, text="⚙️ 高级设置", padding="15")
        advanced_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        advanced_frame.columnconfigure(0, weight=1)
        
        # 上传间隔设置
        interval_frame = ttk.Frame(advanced_frame)
        interval_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        interval_frame.columnconfigure(1, weight=1)
        
        ttk.Label(interval_frame, text="上传间隔:", style='Subheader.TLabel', width=12).grid(row=0, column=0, sticky=tk.W)
        self.upload_interval_var = tk.StringVar(value=self.config.get('AUTOMATION', 'upload_interval', fallback='5'))
        interval_spinbox = ttk.Spinbox(interval_frame, 
                                      from_=1, to=60, 
                                      textvariable=self.upload_interval_var,
                                      width=10,
                                      font=('Segoe UI', 10))
        interval_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(interval_frame, text="秒", style='Subheader.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(5, 0))
        
        # 超时设置
        timeout_frame = ttk.Frame(advanced_frame)
        timeout_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        timeout_frame.columnconfigure(1, weight=1)
        
        ttk.Label(timeout_frame, text="操作超时:", style='Subheader.TLabel', width=12).grid(row=0, column=0, sticky=tk.W)
        self.timeout_var = tk.StringVar(value=self.config.get('AUTOMATION', 'timeout', fallback='30'))
        timeout_spinbox = ttk.Spinbox(timeout_frame, 
                                     from_=10, to=300, 
                                     textvariable=self.timeout_var,
                                     width=10,
                                     font=('Segoe UI', 10))
        timeout_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Label(timeout_frame, text="秒", style='Subheader.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(5, 0))
        
    def create_log_panel(self, parent):
        """创建现代化日志显示面板"""
        log_frame = ttk.LabelFrame(parent, text="📋 实时日志", padding="15")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)
        
        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        log_toolbar.columnconfigure(0, weight=1)
        
        # 日志标题和清除按钮
        ttk.Label(log_toolbar, text="📝 系统运行日志", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        clear_btn = ttk.Button(log_toolbar, text="🗑️ 清除日志", command=self.clear_logs)
        clear_btn.grid(row=0, column=1, sticky=tk.E)
        
        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 height=10, 
                                                 wrap=tk.WORD,
                                                 font=('Consolas', 9),
                                                 bg=self.colors['light_gray'],
                                                 fg=self.colors['text_dark'],
                                                 borderwidth=2,
                                                 relief='solid')
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def create_status_bar(self, parent):
        """创建现代化状态栏"""
        status_frame = ttk.LabelFrame(parent, text="📊 系统状态", padding="15")
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态信息区域
        status_info = ttk.Frame(status_frame)
        status_info.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_info.columnconfigure(1, weight=1)
        
        # 状态标签
        ttk.Label(status_info, text="当前状态:", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.status_var = tk.StringVar(value="🟢 系统就绪")
        self.status_label = ttk.Label(status_info, textvariable=self.status_var, font=('Segoe UI', 10, 'bold'))
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(15, 0))
        
        # 进度条区域
        progress_frame = ttk.Frame(status_frame)
        progress_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        progress_frame.columnconfigure(1, weight=1)
        
        ttk.Label(progress_frame, text="进度:", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, 
                                          variable=self.progress_var, 
                                          maximum=100,
                                          length=400,
                                          style='TProgressbar')
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(15, 0))
        
    def setup_logging(self):
        """设置日志系统"""
        import logging
        from loguru import logger
        import sys

        # 移除默认的loguru处理器
        logger.remove()

        # 创建自定义日志处理器
        class GUILogHandler:
            def __init__(self, log_queue):
                self.log_queue = log_queue

            def write(self, message):
                if message.strip():
                    self.log_queue.put(message)

        # 添加GUI日志处理器
        gui_handler = GUILogHandler(self.log_queue)

        # 配置loguru输出到GUI
        logger.add(gui_handler.write, level="INFO",
                  format="{time:HH:mm:ss} | {level} | {message}")

        # 同时输出到控制台（用于调试）
        logger.add(sys.stderr, level="DEBUG")
        
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}\n"
        self.log_queue.put(formatted_message)
        
    def update_logs(self):
        """更新日志显示"""
        try:
            while True:
                log_entry = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, log_entry)
                self.log_text.see(tk.END)
        except queue.Empty:
            pass
        
        # 每100ms检查一次
        self.root.after(100, self.update_logs)

    def save_current_config(self):
        """保存当前配置"""
        try:
            # 确保所有必要的配置段存在
            if not self.config.has_section('BROWSER'):
                self.config.add_section('BROWSER')
            if not self.config.has_section('HUBSTUDIO'):
                self.config.add_section('HUBSTUDIO')
            if not self.config.has_section('VIDEO'):
                self.config.add_section('VIDEO')
            if not self.config.has_section('AUTOMATION'):
                self.config.add_section('AUTOMATION')
            
            # 获取选中的环境ID
            selected_env = self.environment_var.get()
            if selected_env and " - " in selected_env:
                profile_id = selected_env.split(" - ")[0]
                self.config.set('BROWSER', 'profile_id', profile_id)

            # 保存API配置
            self.config.set('BROWSER', 'api_url', self.api_url_var.get())
            self.config.set('HUBSTUDIO', 'api_id', self.api_id_var.get())
            self.config.set('HUBSTUDIO', 'api_secret', self.api_secret_var.get())
            
            # 保存视频配置
            self.config.set('VIDEO', 'default_title', self.title_var.get())
            
            # 获取描述文本
            description = self.desc_text.get('1.0', tk.END).strip()
            self.config.set('VIDEO', 'default_description', description)
            
            # 保存高级设置
            if hasattr(self, 'upload_interval_var'):
                self.config.set('AUTOMATION', 'upload_interval', self.upload_interval_var.get())
            if hasattr(self, 'timeout_var'):
                self.config.set('AUTOMATION', 'timeout', self.timeout_var.get())
            
            # 写入配置文件
            self.save_config()
            self.log_message("配置已保存成功", "INFO")
            messagebox.showinfo("成功", "配置已保存成功")
        except Exception as e:
            self.log_message(f"保存配置失败: {e}", "ERROR")
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def open_config_window(self):
        """打开配置管理窗口"""
        try:
            from config_gui import ConfigWindow
            ConfigWindow(self.root, self.config, self.reload_config)
        except ImportError:
            messagebox.showerror("错误", "无法加载配置管理模块")
        except Exception as e:
            messagebox.showerror("错误", f"打开配置窗口失败: {e}")

    def reload_config(self):
        """重新加载配置"""
        try:
            self.load_config()
            # 更新界面显示的配置值
            self.api_url_var.set(self.config.get('BROWSER', 'api_url', fallback='http://localhost:6873'))
            self.api_id_var.set(self.config.get('HUBSTUDIO', 'api_id', fallback=''))
            self.api_secret_var.set(self.config.get('HUBSTUDIO', 'api_secret', fallback=''))
            self.title_var.set(self.config.get('VIDEO', 'default_title', fallback='自动上传视频'))
            
            # 更新描述文本
            description = self.config.get('VIDEO', 'default_description', fallback='')
            self.desc_text.delete('1.0', tk.END)
            self.desc_text.insert('1.0', description)
            
            self.log_message("配置已重新加载", "INFO")
            # 重新刷新环境列表
            self.refresh_environments()
        except Exception as e:
            self.log_message(f"重新加载配置失败: {e}", "ERROR")

    def refresh_environments(self):
        """刷新HubStudio环境列表"""
        def refresh_in_thread():
            try:
                self.log_message("正在获取HubStudio环境列表...", "INFO")

                import requests
                import time
                import hashlib
                
                api_url = self.api_url_var.get()
                api_id = self.api_id_var.get().strip()
                api_secret = self.api_secret_var.get().strip()
                
                # 检查API凭证
                if not api_id or not api_secret:
                    self.root.after(0, lambda: self.hubstudio_status_var.set("请配置API凭证"))
                    self.root.after(0, lambda: self.status_display.config(foreground='red'))
                    self.root.after(0, lambda: self.api_status_var.set("未连接"))
                    self.root.after(0, lambda: self.api_status_label.config(foreground='red'))
                    self.log_message("请在设置中配置HubStudio API ID和API Secret", "ERROR")
                    return
                
                # 准备认证参数
                timestamp = str(int(time.time() * 1000))
                
                # 构建请求数据
                request_data = {
                    "current": 1,
                    "size": 200
                }
                
                # 构建签名（根据HubStudio文档：SHA256(apikey + timestamp + params)）
                params_str = ""
                sign_string = api_id + timestamp + params_str
                signature = hashlib.sha256(sign_string.encode()).hexdigest()
                
                # 构建请求头
                headers = {
                    'Content-Type': 'application/json',
                    'api_key': api_id,
                    'req_time': timestamp,
                    'sign': signature
                }
                
                self.log_message(f"使用API ID: {api_id[:10]}...", "DEBUG")
                self.log_message(f"请求时间戳: {timestamp}", "DEBUG")
                
                # 获取所有环境列表
                response = requests.post(f"{api_url}/api/v1/env/list",
                                       json=request_data,
                                       headers=headers,
                                       timeout=10)
                
                self.log_message(f"API响应状态: {response.status_code}", "DEBUG")
                
                if response.status_code == 200:
                    result = response.json()
                    self.log_message(f"API响应: {result}", "DEBUG")
                    
                    if result.get('code') == 0:
                        containers = result.get('data', {}).get('list', [])
                        self.log_message(f"获取到 {len(containers)} 个环境", "INFO")

                        # 更新环境列表
                        env_list = []
                        available_count = 0

                        # 获取每个环境的状态
                        if containers:
                            # 提取所有环境ID
                            container_codes = [str(container.get('containerCode', '')) for container in containers]
                            
                            # 构建状态查询的签名
                            status_timestamp = str(int(time.time() * 1000))
                            status_sign_string = api_id + status_timestamp + ""
                            status_signature = hashlib.sha256(status_sign_string.encode()).hexdigest()
                            
                            status_headers = {
                                'Content-Type': 'application/json',
                                'api_key': api_id,
                                'req_time': status_timestamp,
                                'sign': status_signature
                            }

                            # 获取环境状态
                            status_response = requests.post(f"{api_url}/api/v1/browser/all-browser-status",
                                                          json={"containerCodes": container_codes},
                                                          headers=status_headers,
                                                          timeout=10)

                            status_data = {}
                            if status_response.status_code == 200:
                                status_result = status_response.json()
                                if status_result.get('code') == 0:
                                    status_containers = status_result.get('data', {}).get('containers', [])
                                    for status_container in status_containers:
                                        code = status_container.get('containerCode', '')
                                        status = status_container.get('status', 3)
                                        status_data[str(code)] = status
                                else:
                                    self.log_message(f"获取环境状态失败: {status_result.get('msg')}", "WARNING")
                            else:
                                self.log_message(f"状态查询API请求失败: {status_response.status_code}", "WARNING")

                        for container in containers:
                            container_code = str(container.get('containerCode', ''))
                            container_name = container.get('containerName', '')

                            # 获取状态，默认为已关闭
                            status = status_data.get(container_code, 3)
                            status_map = {0: '已开启', 1: '开启中', 2: '关闭中', 3: '已关闭'}
                            status_text = status_map.get(status, '未知')

                            env_display = f"{container_code} - {container_name} - {status_text}"
                            env_list.append(env_display)

                            if status == 3:  # 已关闭的环境可以使用
                                available_count += 1

                        if env_list:
                            self.root.after(0, lambda: self.hubstudio_status_var.set(f"发现 {len(env_list)} 个环境 (可用: {available_count})"))
                            self.root.after(0, lambda: self.status_display.config(foreground='green'))
                            self.root.after(0, lambda: self.api_status_var.set("🟢 已连接"))
                            self.root.after(0, lambda: self.env_count_var.set(f"{len(env_list)} 个 (可用: {available_count})"))
                            self.root.after(0, lambda: self._update_env_list_display(env_list))
                            self.log_message(f"发现 {len(env_list)} 个HubStudio环境，其中 {available_count} 个可用", "INFO")
                        else:
                            self.root.after(0, lambda: self.hubstudio_status_var.set("未发现环境"))
                            self.root.after(0, lambda: self.status_display.config(foreground='orange'))
                            self.root.after(0, lambda: self.api_status_var.set("🟡 已连接"))
                            self.root.after(0, lambda: self.env_count_var.set("0 个"))
                            self.root.after(0, lambda: self._update_env_list_display([]))
                            self.log_message("未发现任何HubStudio环境", "WARNING")
                            self.log_message("请在HubStudio客户端中创建环境", "INFO")
                    else:
                        self.root.after(0, lambda: self.hubstudio_status_var.set("API调用失败"))
                        self.root.after(0, lambda: self.status_display.config(foreground='red'))
                        self.root.after(0, lambda: self.api_status_var.set("🔴 未连接"))
                        self.root.after(0, lambda: self.env_count_var.set("0 个"))
                        self.root.after(0, lambda: self._update_env_list_display([]))
                        self.log_message(f"API调用失败: {result.get('msg', '未知错误')}", "ERROR")
                        
                        # 检查是否是认证问题
                        if result.get('code') == 401 or 'auth' in str(result.get('msg', '')).lower():
                            self.log_message("可能是API认证失败，请检查API ID和API Secret是否正确", "ERROR")
                        elif result.get('code') == 403:
                            self.log_message("API权限不足，请检查API坐席是否可用", "ERROR")
                else:
                    self.root.after(0, lambda: self.hubstudio_status_var.set("连接失败"))
                    self.root.after(0, lambda: self.status_display.config(foreground='red'))
                    self.root.after(0, lambda: self.api_status_var.set("🔴 未连接"))
                    self.root.after(0, lambda: self.env_count_var.set("0 个"))
                    self.root.after(0, lambda: self._update_env_list_display([]))
                    self.log_message(f"HubStudio连接失败: HTTP {response.status_code}", "ERROR")
                    
                    if response.status_code == 401:
                        self.log_message("认证失败，请检查API凭证是否正确", "ERROR")
                    elif response.status_code == 403:
                        self.log_message("权限不足，请检查API坐席是否可用", "ERROR")
                    elif response.status_code == 404:
                        self.log_message("API端点不存在，请检查API地址是否正确", "ERROR")
                    
                    try:
                        error_detail = response.json()
                        self.log_message(f"错误详情: {error_detail}", "DEBUG")
                    except:
                        self.log_message(f"响应内容: {response.text[:200]}", "DEBUG")

            except requests.exceptions.ConnectionError:
                self.root.after(0, lambda: self.hubstudio_status_var.set("连接失败"))
                self.root.after(0, lambda: self.status_display.config(foreground='red'))
                self.root.after(0, lambda: self.api_status_var.set("🔴 未连接"))
                self.root.after(0, lambda: self.env_count_var.set("0 个"))
                self.root.after(0, lambda: self._update_env_list_display([]))
                self.log_message("无法连接到HubStudio API", "ERROR")
                self.log_message("请检查：1. HubStudio客户端是否已启动 2. API地址是否正确", "INFO")
            except requests.exceptions.Timeout:
                self.root.after(0, lambda: self.hubstudio_status_var.set("连接超时"))
                self.root.after(0, lambda: self.status_display.config(foreground='red'))
                self.root.after(0, lambda: self.api_status_var.set("🔴 未连接"))
                self.root.after(0, lambda: self.env_count_var.set("0 个"))
                self.root.after(0, lambda: self._update_env_list_display([]))
                self.log_message("API请求超时，请检查网络连接", "ERROR")
            except Exception as e:
                self.root.after(0, lambda: self.hubstudio_status_var.set("连接异常"))
                self.root.after(0, lambda: self.status_display.config(foreground='red'))
                self.root.after(0, lambda: self.api_status_var.set("🔴 未连接"))
                self.root.after(0, lambda: self.env_count_var.set("0 个"))
                self.root.after(0, lambda: self._update_env_list_display([]))
                self.log_message(f"获取环境列表异常: {e}", "ERROR")
                self.log_message(f"API地址: {self.api_url_var.get()}", "DEBUG")
                self.log_message(f"API ID: {self.api_id_var.get()[:10] if self.api_id_var.get() else '未配置'}...", "DEBUG")
                import traceback
                self.log_message(f"详细错误: {traceback.format_exc()}", "DEBUG")

        threading.Thread(target=refresh_in_thread, daemon=True).start()
    
    def _update_env_list_display(self, env_list):
        """更新环境列表显示"""
        try:
            self.env_list_text.config(state='normal')
            self.env_list_text.delete('1.0', tk.END)
            
            if env_list:
                for i, env in enumerate(env_list, 1):
                    self.env_list_text.insert(tk.END, f"{i}. {env}\n")
            else:
                self.env_list_text.insert(tk.END, "暂无可用环境\n请检查HubStudio连接状态")
            
            self.env_list_text.config(state='disabled')
        except Exception as e:
            self.log_message(f"更新环境列表显示失败: {e}", "ERROR")

    def test_hubstudio_connection(self):
        """测试HubStudio连接"""
        def test_in_thread():
            try:
                self.log_message("正在测试HubStudio连接...", "INFO")

                import requests
                import time
                import hashlib
                
                api_url = self.api_url_var.get()
                api_id = self.api_id_var.get().strip()
                api_secret = self.api_secret_var.get().strip()
                
                self.log_message(f"测试API地址: {api_url}", "DEBUG")
                self.log_message(f"使用API ID: {api_id[:10] if api_id else '未配置'}...", "DEBUG")
                
                # 检查API凭证
                if not api_id or not api_secret:
                    self.log_message("❌ API凭证未配置，请先配置API ID和API Secret", "ERROR")
                    return
                
                # 准备认证参数
                timestamp = str(int(time.time() * 1000))
                
                # 构建请求数据
                request_data = {
                    "current": 1,
                    "size": 1
                }
                
                # 构建签名
                params_str = ""
                sign_string = api_id + timestamp + params_str
                signature = hashlib.sha256(sign_string.encode()).hexdigest()
                
                # 构建请求头
                headers = {
                    'Content-Type': 'application/json',
                    'api_key': api_id,
                    'req_time': timestamp,
                    'sign': signature
                }
                
                # 测试基本连接
                response = requests.post(f"{api_url}/api/v1/env/list",
                                       json=request_data,
                                       headers=headers,
                                       timeout=10)
                
                self.log_message(f"连接响应状态: {response.status_code}", "DEBUG")

                if response.status_code == 200:
                    result = response.json()
                    self.log_message(f"API响应: {result}", "DEBUG")
                    
                    if result.get('code') == 0:
                        self.log_message("✅ HubStudio API连接成功", "INFO")
                        
                        # 获取完整环境列表进行测试
                        full_request_data = {
                            "current": 1,
                            "size": 200
                        }
                        
                        # 重新生成签名
                        full_timestamp = str(int(time.time() * 1000))
                        full_sign_string = api_id + full_timestamp + ""
                        full_signature = hashlib.sha256(full_sign_string.encode()).hexdigest()
                        
                        full_headers = {
                            'Content-Type': 'application/json',
                            'api_key': api_id,
                            'req_time': full_timestamp,
                            'sign': full_signature
                        }
                        
                        full_response = requests.post(f"{api_url}/api/v1/env/list",
                                                   json=full_request_data,
                                                   headers=full_headers,
                                                   timeout=10)

                        if full_response.status_code == 200:
                            full_result = full_response.json()
                            if full_result.get('code') == 0:
                                containers = full_result.get('data', {}).get('list', [])
                                self.log_message(f"✅ 发现 {len(containers)} 个环境", "INFO")

                                for container in containers:
                                    container_code = container.get('containerCode', '')
                                    container_name = container.get('containerName', '')
                                    self.log_message(f"  环境 {container_code} - {container_name}", "INFO")
                                
                                # 测试成功后自动刷新环境状态
                                self.log_message("正在刷新环境状态...", "INFO")
                                self.root.after(1000, self.refresh_environments)
                            else:
                                self.log_message(f"❌ 获取环境列表失败: {full_result.get('msg')}", "ERROR")
                        else:
                            self.log_message(f"❌ 获取完整环境列表失败: HTTP {full_response.status_code}", "ERROR")
                    else:
                        self.log_message(f"❌ API认证失败: {result.get('msg')}", "ERROR")
                        
                        # 检查具体错误类型
                        if result.get('code') == 401:
                            self.log_message("请检查API ID和API Secret是否正确", "ERROR")
                        elif result.get('code') == 403:
                            self.log_message("请检查API坐席是否可用", "ERROR")
                else:
                    self.log_message(f"❌ HubStudio连接失败: HTTP {response.status_code}", "ERROR")
                    
                    if response.status_code == 401:
                        self.log_message("认证失败，请检查API凭证是否正确", "ERROR")
                    elif response.status_code == 403:
                        self.log_message("权限不足，请检查API坐席是否可用", "ERROR")
                    elif response.status_code == 404:
                        self.log_message("API端点不存在，请检查API地址是否正确", "ERROR")
                    
                    try:
                        error_detail = response.json()
                        self.log_message(f"错误详情: {error_detail}", "DEBUG")
                    except:
                        self.log_message(f"响应内容: {response.text[:200]}", "DEBUG")

            except requests.exceptions.ConnectionError:
                self.log_message("❌ 连接被拒绝，请检查HubStudio是否已启动", "ERROR")
                self.log_message("请确保HubStudio客户端正在运行并且API服务已启用", "INFO")
            except requests.exceptions.Timeout:
                self.log_message("❌ 连接超时，请检查网络和API地址", "ERROR")
            except Exception as e:
                self.log_message(f"❌ 连接测试异常: {e}", "ERROR")
                import traceback
                self.log_message(f"详细错误: {traceback.format_exc()}", "DEBUG")

        threading.Thread(target=test_in_thread, daemon=True).start()

    def show_help(self):
        """显示帮助信息"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.resizable(True, True)

        # 创建滚动文本框
        text_frame = ttk.Frame(help_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Microsoft YaHei', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 帮助内容
        help_content = """🎬 HubStudio + YouTube 自动化上传工具 - 使用帮助

📋 快速开始：

1️⃣ 确保HubStudio客户端正在运行
2️⃣ 在HubStudio中创建至少一个浏览器环境
3️⃣ 添加要上传的视频文件
4️⃣ 点击"开始自动上传"

🔧 环境设置：

⚠️ 重要：只有Chrome内核环境支持API访问！

如果显示"未发现环境"，请按以下步骤操作：

1. 打开HubStudio客户端
2. 点击"新建环境"或"创建环境"
3. ⭐ 选择浏览器类型：Chrome（重要！不要选Firebrowser）
4. 设置环境名称（如"YouTube上传"）
5. 完成创建，确保状态为"已关闭"
6. 点击"刷新HubStudio环境"按钮

🚫 Firebrowser内核环境不支持自动化，必须使用Chrome！

🚨 常见问题：

❌ "连接失败"
   → 确保HubStudio客户端正在运行
   → 检查防火墙设置
   → 确认API端口6873未被占用

❌ "未发现环境"
   → 在HubStudio中创建Chrome内核环境（不要选Firebrowser）
   → 确保环境状态为"已关闭"（可用状态）
   → Firebrowser内核不支持API访问

❌ "环境正在使用中"
   → 在HubStudio中关闭正在运行的环境
   → 等待状态变为"已关闭"

🔍 诊断工具：

• 点击"测试HubStudio连接"按钮
• 查看日志区域的详细信息
• 运行命令：python test_hubstudio.py

📁 视频管理：

• 支持格式：MP4, AVI, MOV, MKV, FLV, WMV, WEBM
• 可以添加单个文件或整个文件夹
• 支持批量上传和进度跟踪

⚙️ 高级配置：

• 点击"高级配置"按钮自定义设置
• 可以修改默认标题和描述
• 支持自定义API地址

📞 获取更多帮助：

• 查看 README.md 文件
• 查看 HUBSTUDIO_SETUP.md 详细设置指南
• 运行 python test_hubstudio.py 进行诊断

🎉 成功标志：

当一切设置正确时，你应该看到：
✅ HubStudio状态：发现 X 个环境
✅ 环境下拉框中有可选项
✅ 可以正常开始视频上传"""

        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)  # 只读

        # 关闭按钮
        close_btn = ttk.Button(help_window, text="关闭", command=help_window.destroy)
        close_btn.pack(pady=10)

    def add_video_files(self):
        """选择单个视频文件"""
        try:
            file_types = [
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm"),
                ("MP4文件", "*.mp4"),
                ("所有文件", "*.*")
            ]

            file_path = filedialog.askopenfilename(
                title="选择视频文件（只能选择一个）",
                filetypes=file_types
            )

            if file_path:
                # 清空之前的选择，只保留一个视频
                self.video_list = [file_path]
                self.update_video_count()
                self.log_message(f"已选择视频文件: {os.path.basename(file_path)}", "INFO")
                
        except Exception as e:
            error_msg = f"选择视频文件失败: {e}"
            self.log_message(error_msg, "ERROR")
            messagebox.showerror("错误", error_msg)
    
    def clear_video_selection(self):
        """清除视频选择"""
        self.video_list = []
        self.update_video_count()
        self.log_message("已清除视频选择", "INFO")
    
    def update_video_count(self):
        """更新视频显示"""
        count = len(self.video_list)
        if count > 0:
            video_path = self.video_list[0]
            video_name = os.path.basename(video_path)
            self.video_count_var.set(f"已选择: {video_name}")
            self.video_path_var.set(video_path)
        else:
            self.video_count_var.set("未选择视频")
            self.video_path_var.set("")
        
        # 启用或禁用上传按钮
        if count > 0 and not self.is_uploading:
            self.upload_btn.config(state='normal')
        else:
            self.upload_btn.config(state='disabled')









    def clear_logs(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)



    def start_upload(self):
        """开始上传视频"""
        if self.is_uploading:
            self.log_message("上传正在进行中...", "WARNING")
            return
            
        if not self.video_list:
            messagebox.showwarning("警告", "请先添加视频文件")
            return
        
        # 自动获取所有可用环境
        available_envs = self.get_all_available_environments()
        if not available_envs:
            messagebox.showwarning("警告", "没有可用的HubStudio环境，请检查HubStudio连接")
            return
        
        # 获取并发数量
        concurrent_count = self.concurrent_count_var.get()
        actual_concurrent = min(concurrent_count, len(available_envs))
        
        # 确认开始上传
        video_name = os.path.basename(self.video_list[0])
        if not messagebox.askyesno("确认", 
                                  f"将使用 {actual_concurrent} 个浏览器环境并发上传同一个视频\n"
                                  f"视频文件: {video_name}\n"
                                  f"可用环境: {len(available_envs)} 个\n"
                                  f"每个环境都会上传这个视频（标题会自动区分）\n"
                                  f"确定要开始上传吗？"):
            return
            
        # 获取配置
        title = self.title_var.get().strip()
        description = self.desc_text.get('1.0', tk.END).strip()
        children_content = self.children_content_var.get() == "是"
        
        if not title:
            title = "自动上传视频"
            
        if not description:
            description = "通过HubStudio自动化脚本上传到YouTube的视频"
            
        self.log_message(f"开始并发上传视频: {video_name}", "INFO")
        self.log_message(f"并发数量: {actual_concurrent} 个环境", "INFO")
        self.log_message(f"可用环境: {len(available_envs)} 个", "INFO")
        self.log_message(f"使用环境: {[env.split(' - ')[0] for env in available_envs[:actual_concurrent]]}", "INFO")
        self.log_message(f"标题模板: {title}", "INFO")
        self.log_message(f"描述模板: {description[:50]}...", "INFO")
        self.log_message(f"是否为儿童专属: {children_content}", "INFO")
        
        # 更新状态
        self.is_uploading = True
        self.upload_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.status_var.set("🚀 正在初始化并发上传...")
        
        # 在新线程中执行并发上传
        upload_thread = threading.Thread(
            target=self.concurrent_upload_videos_thread,
            args=(available_envs[:actual_concurrent], title, description, children_content),
            daemon=True
        )
        upload_thread.start()
    
    def get_all_available_environments(self):
        """自动获取所有可用环境列表（按顺序）"""
        available_envs = []
        
        try:
            import requests
            import time
            import hashlib
            
            api_url = self.api_url_var.get()
            api_id = self.api_id_var.get().strip()
            api_secret = self.api_secret_var.get().strip()
            
            # 检查API凭证
            if not api_id or not api_secret:
                self.log_message("API凭证未配置，无法获取环境列表", "ERROR")
                return []
            
            # 准备认证参数
            timestamp = str(int(time.time() * 1000))
            
            # 构建请求数据
            request_data = {
                "current": 1,
                "size": 200
            }
            
            # 构建签名
            params_str = ""
            sign_string = api_id + timestamp + params_str
            signature = hashlib.sha256(sign_string.encode()).hexdigest()
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'api_key': api_id,
                'req_time': timestamp,
                'sign': signature
            }
            
            # 获取环境列表
            response = requests.post(f"{api_url}/api/v1/env/list",
                                   json=request_data,
                                   headers=headers,
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    containers = result.get('data', {}).get('list', [])
                    
                    # 获取环境状态
                    status_data = {}
                    if containers:
                        container_codes = [str(container.get('containerCode', '')) for container in containers]
                        
                        # 构建状态查询的签名
                        status_timestamp = str(int(time.time() * 1000))
                        status_sign_string = api_id + status_timestamp + ""
                        status_signature = hashlib.sha256(status_sign_string.encode()).hexdigest()
                        
                        status_headers = {
                            'Content-Type': 'application/json',
                            'api_key': api_id,
                            'req_time': status_timestamp,
                            'sign': status_signature
                        }
                        
                        try:
                            status_response = requests.post(f"{api_url}/api/v1/browser/all-browser-status",
                                                          json={"containerCodes": container_codes},
                                                          headers=status_headers,
                                                          timeout=10)
                            
                            if status_response.status_code == 200:
                                status_result = status_response.json()
                                if status_result.get('code') == 0:
                                    status_containers = status_result.get('data', {}).get('containers', [])
                                    for status_container in status_containers:
                                        code = status_container.get('containerCode', '')
                                        status = status_container.get('status', 3)
                                        status_data[str(code)] = status
                        except Exception as e:
                            self.log_message(f"获取环境状态失败: {e}", "WARNING")
                    
                    # 按环境ID排序，确保顺序一致
                    sorted_containers = sorted(containers, key=lambda x: x.get('containerCode', 0))
                    
                    for container in sorted_containers:
                        container_code = str(container.get('containerCode', ''))
                        container_name = container.get('containerName', '')
                        
                        # 获取状态，默认为已关闭
                        status = status_data.get(container_code, 3)
                        
                        # 只选择已关闭的环境（可以启动的环境）
                        if status == 3:  # 3表示已关闭
                            env_display = f"{container_code} - {container_name} - 已关闭"
                            available_envs.append(env_display)
                    
                    self.log_message(f"检测到 {len(available_envs)} 个可用环境", "INFO")
                else:
                    self.log_message(f"获取环境列表失败: {result.get('msg', '未知错误')}", "ERROR")
            else:
                self.log_message(f"API请求失败: HTTP {response.status_code}", "ERROR")
                
        except Exception as e:
            self.log_message(f"获取环境列表失败: {e}", "ERROR")
            
        return available_envs

    def stop_upload(self):
        """停止上传"""
        if self.is_uploading:
            self.is_uploading = False
            self.log_message("正在停止所有并发上传任务...", "INFO")
            self.status_var.set("⏹️ 正在停止上传...")
            # 并发上传会检查self.is_uploading标志来停止

    def concurrent_upload_videos_thread(self, selected_envs, title_template, description_template, children_content):
        """并发上传视频的线程函数"""
        import concurrent.futures
        
        try:
            # 获取要上传的视频（只取第一个视频）
            if not self.video_list:
                self.log_message("没有选择视频文件", "ERROR")
                self.root.after(0, lambda: self.upload_finished(False))
                return
                
            video_path = self.video_list[0]  # 只上传第一个选择的视频
            concurrent_count = len(selected_envs)
            
            self.log_message(f"初始化 {concurrent_count} 个并发上传任务", "INFO")
            self.log_message(f"所有环境将上传同一个视频: {os.path.basename(video_path)}", "INFO")
            
            # 创建结果统计
            self.upload_results = {
                'total': concurrent_count,  # 总数等于并发数
                'completed': 0,
                'success': 0,
                'failed': 0,
                'lock': threading.Lock()
            }
            
            # 使用线程池执行并发上传
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
                # 为每个环境创建上传任务
                futures = []
                for i, env in enumerate(selected_envs):
                    future = executor.submit(
                        self.single_env_upload_worker_same_video,
                        env, video_path, title_template, description_template, children_content, i+1
                    )
                    futures.append(future)
                
                # 等待所有任务完成
                concurrent.futures.wait(futures)
            
            # 完成上传
            self.root.after(0, lambda: self.progress_var.set(100))
            success_rate = (self.upload_results['success'] / concurrent_count) * 100 if concurrent_count > 0 else 0
            self.log_message(f"并发上传完成！成功: {self.upload_results['success']}/{concurrent_count} ({success_rate:.1f}%)", "INFO")
            self.root.after(0, lambda: self.upload_finished(self.upload_results['success'] > 0))
            
        except Exception as e:
            error_msg = f"并发上传过程中发生错误: {e}"
            self.log_message(error_msg, "ERROR")
            self.root.after(0, lambda: self.upload_finished(False))
    
    def single_env_upload_worker_same_video(self, env, video_path, title_template, description_template, children_content, worker_id):
        """单个环境上传同一个视频的工作线程"""
        automation = None
        try:
            # 检查上传状态
            if not self.is_uploading:
                self.log_message(f"工作线程 {worker_id} 检测到上传已停止，退出", "INFO")
                return
                
            # 提取环境ID
            profile_id = env.split(" - ")[0]
            env_name = env.split(" - ")[1] if " - " in env else profile_id
            
            self.log_message(f"🚀 工作线程 {worker_id} 启动，使用环境: {env_name} (ID: {profile_id})", "INFO")
            
            # 初始化自动化系统
            self.log_message(f"工作线程 {worker_id} 正在初始化自动化系统...", "INFO")
            automation = HubStudioYouTubeAutomation('config.ini')
            
            self.log_message(f"工作线程 {worker_id} 正在启动浏览器环境...", "INFO")
            if not automation.initialize(profile_id):
                self.log_message(f"❌ 工作线程 {worker_id} 初始化失败 - 环境: {env_name}", "ERROR")
                with self.upload_results['lock']:
                    self.upload_results['completed'] += 1
                    self.upload_results['failed'] += 1
                return
                
            self.log_message(f"✅ 工作线程 {worker_id} 初始化成功 - 环境: {env_name}", "INFO")
            
            self.log_message(f"工作线程 {worker_id} 初始化成功，开始上传视频", "INFO")
            
            try:
                # 使用用户输入的标题，不添加后缀
                if title_template:
                    title = title_template
                else:
                    title = os.path.splitext(os.path.basename(video_path))[0]
                
                description = description_template if description_template else "通过HubStudio自动化脚本上传到YouTube的视频"
                
                self.log_message(f"工作线程 {worker_id} 开始上传: {os.path.basename(video_path)}", "INFO")
                
                # 执行上传
                success = automation.upload_single_video(
                    video_path, title, description, children_content
                )
                
                # 更新统计
                with self.upload_results['lock']:
                    self.upload_results['completed'] += 1
                    if success:
                        self.upload_results['success'] += 1
                        self.log_message(f"✅ 工作线程 {worker_id} 上传成功: {os.path.basename(video_path)}", "INFO")
                    else:
                        self.upload_results['failed'] += 1
                        self.log_message(f"❌ 工作线程 {worker_id} 上传失败: {os.path.basename(video_path)}", "ERROR")
                    
                    # 更新进度
                    progress = (self.upload_results['completed'] / self.upload_results['total']) * 100
                    self.root.after(0, lambda p=progress: self.progress_var.set(p))
                    self.root.after(0, lambda: self.status_var.set(
                        f"上传中... ({self.upload_results['completed']}/{self.upload_results['total']}) "
                        f"成功: {self.upload_results['success']}"))
                        
            except Exception as e:
                with self.upload_results['lock']:
                    self.upload_results['completed'] += 1
                    self.upload_results['failed'] += 1
                self.log_message(f"工作线程 {worker_id} 上传异常: {os.path.basename(video_path)} - {e}", "ERROR")
            
            self.log_message(f"工作线程 {worker_id} 完成任务", "INFO")
            
        except Exception as e:
            self.log_message(f"工作线程 {worker_id} 异常: {e}", "ERROR")
        finally:
            # 清理资源
            if automation:
                try:
                    automation.cleanup()
                except:
                    pass
            self.log_message(f"工作线程 {worker_id} 已清理资源", "INFO")
    
    def single_env_upload_worker(self, env, video_queue, title_template, description_template, children_content, worker_id):
        """单个环境的上传工作线程"""
        automation = None
        try:
            # 提取环境ID
            profile_id = env.split(" - ")[0]
            env_name = env.split(" - ")[1] if " - " in env else profile_id
            
            self.log_message(f"工作线程 {worker_id} 启动，使用环境: {env_name} (ID: {profile_id})", "INFO")
            
            # 初始化自动化系统
            automation = HubStudioYouTubeAutomation('config.ini')
            if not automation.initialize(profile_id):
                self.log_message(f"工作线程 {worker_id} 初始化失败", "ERROR")
                return
            
            self.log_message(f"工作线程 {worker_id} 初始化成功，开始处理视频", "INFO")
            
            # 处理视频队列
            while self.is_uploading:
                try:
                    # 使用阻塞方式获取任务，超时1秒
                    video_index, video_path = video_queue.get(timeout=1)
                except queue.Empty:
                    # 队列为空，检查是否还有其他线程在工作
                    if video_queue.empty():
                        break
                    continue
                
                try:
                    # 生成唯一标题（避免重复）
                    if title_template:
                        title = f"{title_template} - {video_index + 1:03d}"
                    else:
                        title = os.path.splitext(os.path.basename(video_path))[0]
                    
                    description = description_template if description_template else "通过HubStudio自动化脚本上传到YouTube的视频"
                    
                    self.log_message(f"工作线程 {worker_id} 开始上传: {os.path.basename(video_path)}", "INFO")
                    
                    # 执行上传
                    success = automation.upload_single_video(
                        video_path, title, description, children_content
                    )
                    
                    # 更新统计
                    with self.upload_results['lock']:
                        self.upload_results['completed'] += 1
                        if success:
                            self.upload_results['success'] += 1
                            self.log_message(f"✅ 工作线程 {worker_id} 上传成功: {os.path.basename(video_path)}", "INFO")
                        else:
                            self.upload_results['failed'] += 1
                            self.log_message(f"❌ 工作线程 {worker_id} 上传失败: {os.path.basename(video_path)}", "ERROR")
                        
                        # 更新进度
                        progress = (self.upload_results['completed'] / self.upload_results['total']) * 100
                        self.root.after(0, lambda p=progress: self.progress_var.set(p))
                        self.root.after(0, lambda: self.status_var.set(
                            f"上传中... ({self.upload_results['completed']}/{self.upload_results['total']}) "
                            f"成功: {self.upload_results['success']}"))
                    
                    # 标记任务完成
                    video_queue.task_done()
                    
                    # 上传间隔
                    if self.is_uploading:
                        interval = int(self.config.get('AUTOMATION', 'upload_interval', fallback=5))
                        time.sleep(interval)
                        
                except Exception as e:
                    with self.upload_results['lock']:
                        self.upload_results['completed'] += 1
                        self.upload_results['failed'] += 1
                    self.log_message(f"工作线程 {worker_id} 上传异常: {os.path.basename(video_path)} - {e}", "ERROR")
                    # 标记任务完成
                    video_queue.task_done()
            
            self.log_message(f"工作线程 {worker_id} 完成所有任务", "INFO")
            
        except Exception as e:
            self.log_message(f"工作线程 {worker_id} 异常: {e}", "ERROR")
        finally:
            # 清理资源
            if automation:
                try:
                    automation.cleanup()
                except:
                    pass
            self.log_message(f"工作线程 {worker_id} 已清理资源", "INFO")
    


    def upload_finished(self, success):
        """上传完成后的处理"""
        self.is_uploading = False
        self.stop_btn.config(state='disabled')
        self.progress_var.set(0)
        
        # 更新上传按钮状态
        self.update_video_count()
        
        if success:
            self.status_var.set("✅ 上传完成")
            messagebox.showinfo("成功", "视频上传完成！")
        else:
            self.status_var.set("❌ 上传失败")
            messagebox.showerror("失败", "视频上传失败！")


def main():
    """主函数"""
    # 创建主窗口
    root = tk.Tk()

    # 创建应用实例
    app = HubStudioGUI(root)

    # 运行主循环
    root.mainloop()


if __name__ == "__main__":
    main()
