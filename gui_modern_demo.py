#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化GUI设计演示
展示改进后的界面设计效果
"""

import tkinter as tk
from tkinter import ttk
import os

class ModernGUIDemo:
    def __init__(self, root):
        self.root = root
        self.root.title("现代化GUI设计演示 - HubStudio + YouTube 自动化上传工具")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        
        # 设置现代化窗口背景色
        self.root.configure(bg='#F8FAFC')
        
        # 设置现代化样式
        self.setup_modern_styles()
        
        # 创建演示界面
        self.create_demo_widgets()
    
    def setup_modern_styles(self):
        """设置现代化界面样式 - 极简主义设计"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 定义极简颜色方案 - 只使用4种主要颜色
        colors = {
            'primary': '#2563EB',           # 主色调 - 现代蓝色
            'surface': '#F8FAFC',           # 表面色 - 极浅灰
            'background': '#FFFFFF',        # 背景色 - 纯白
            'text': '#1E293B',              # 文字色 - 深灰
            'text_secondary': '#64748B',    # 次要文字 - 中灰
            'border': '#E2E8F0',            # 边框色 - 浅灰
            'success': '#10B981',           # 成功色 - 现代绿
            'error': '#EF4444',             # 错误色 - 现代红
            'warning': '#F59E0B'            # 警告色 - 现代橙
        }
        
        # 配置主要组件样式 - 现代卡片式设计
        style.configure('TFrame', 
                       background=colors['background'])
        
        # 现代化卡片样式
        style.configure('Card.TLabelFrame', 
                       background=colors['background'],
                       borderwidth=1, 
                       relief='solid', 
                       bordercolor=colors['border'])
        style.configure('Card.TLabelFrame.Label', 
                       background=colors['background'],
                       foreground=colors['text'], 
                       font=('Segoe UI', 12, 'bold'))
        
        # 主要按钮样式 - 现代扁平设计
        style.configure('Primary.TButton', 
                       background=colors['primary'],
                       foreground=colors['background'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 10, 'bold'),
                       padding=(20, 12))
        style.map('Primary.TButton',
                 background=[('active', '#1D4ED8'),
                           ('pressed', '#1E40AF')])
        
        # 次要按钮样式
        style.configure('Secondary.TButton', 
                       background=colors['surface'],
                       foreground=colors['text'],
                       borderwidth=1,
                       bordercolor=colors['border'],
                       focuscolor='none',
                       font=('Segoe UI', 10),
                       padding=(16, 10))
        style.map('Secondary.TButton',
                 background=[('active', colors['border']),
                           ('pressed', colors['border'])])
        
        # 现代输入框样式
        style.configure('Modern.TEntry',
                       fieldbackground=colors['background'],
                       borderwidth=2,
                       bordercolor=colors['border'],
                       focuscolor=colors['primary'],
                       font=('Segoe UI', 11),
                       padding=12)
        
        # 现代化文字样式
        style.configure('Title.TLabel', 
                       background=colors['background'],
                       foreground=colors['text'],
                       font=('Segoe UI', 24, 'bold'))
        
        style.configure('Subtitle.TLabel', 
                       background=colors['background'],
                       foreground=colors['text_secondary'],
                       font=('Segoe UI', 14))
        
        style.configure('Header.TLabel', 
                       background=colors['background'],
                       foreground=colors['text'],
                       font=('Segoe UI', 14, 'bold'))
        
        style.configure('Subheader.TLabel', 
                       background=colors['background'],
                       foreground=colors['text'],
                       font=('Segoe UI', 12, 'bold'))
        
        style.configure('Body.TLabel', 
                       background=colors['background'],
                       foreground=colors['text_secondary'],
                       font=('Segoe UI', 10))
        
        # 现代进度条样式
        style.configure('Modern.TProgressbar',
                       background=colors['primary'],
                       troughcolor=colors['surface'],
                       borderwidth=0,
                       lightcolor=colors['primary'],
                       darkcolor=colors['primary'])
        
        # 分隔线样式
        style.configure('Modern.TSeparator',
                       background=colors['border'])
        
        # 存储颜色方案
        self.colors = colors
    
    def create_demo_widgets(self):
        """创建演示界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="32")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 标题栏
        main_frame.rowconfigure(1, weight=0)  # 功能演示区
        main_frame.rowconfigure(2, weight=1)  # 对比区域
        
        # 创建标题栏
        self.create_title_section(main_frame)
        
        # 创建功能演示区
        self.create_demo_section(main_frame)
        
        # 创建对比区域
        self.create_comparison_section(main_frame)
    
    def create_title_section(self, parent):
        """创建标题区域"""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 32))
        title_frame.columnconfigure(0, weight=1)
        
        # 主标题
        title_label = ttk.Label(title_frame, text="现代化GUI设计演示", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # 副标题
        subtitle_label = ttk.Label(title_frame, text="极简主义设计 • 统一色彩方案 • 现代化交互体验", style='Subtitle.TLabel')
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(8, 0))
        
        # 分隔线
        separator = ttk.Separator(title_frame, orient='horizontal', style='Modern.TSeparator')
        separator.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(24, 0))
    
    def create_demo_section(self, parent):
        """创建功能演示区域"""
        demo_frame = ttk.LabelFrame(parent, text="现代化组件演示", style='Card.TLabelFrame', padding="24")
        demo_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 24))
        demo_frame.columnconfigure(0, weight=1)
        demo_frame.columnconfigure(1, weight=1)
        demo_frame.columnconfigure(2, weight=1)
        
        # 按钮演示
        button_frame = ttk.LabelFrame(demo_frame, text="按钮样式", style='Card.TLabelFrame', padding="16")
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 8))
        
        ttk.Button(button_frame, text="主要按钮", style='Primary.TButton').pack(pady=8, fill=tk.X)
        ttk.Button(button_frame, text="次要按钮", style='Secondary.TButton').pack(pady=8, fill=tk.X)
        
        # 输入框演示
        input_frame = ttk.LabelFrame(demo_frame, text="输入组件", style='Card.TLabelFrame', padding="16")
        input_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=8)
        
        ttk.Label(input_frame, text="现代化输入框", style='Subheader.TLabel').pack(anchor=tk.W, pady=(0, 8))
        ttk.Entry(input_frame, style='Modern.TEntry').pack(fill=tk.X, pady=(0, 12))
        
        ttk.Label(input_frame, text="说明文字", style='Body.TLabel').pack(anchor=tk.W)
        
        # 进度条演示
        progress_frame = ttk.LabelFrame(demo_frame, text="进度指示", style='Card.TLabelFrame', padding="16")
        progress_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(8, 0))
        
        ttk.Label(progress_frame, text="现代化进度条", style='Subheader.TLabel').pack(anchor=tk.W, pady=(0, 8))
        progress_var = tk.DoubleVar(value=65)
        ttk.Progressbar(progress_frame, variable=progress_var, maximum=100, style='Modern.TProgressbar').pack(fill=tk.X, pady=(0, 12))
        ttk.Label(progress_frame, text="65% 完成", style='Body.TLabel').pack(anchor=tk.W)
    
    def create_comparison_section(self, parent):
        """创建对比区域"""
        comparison_frame = ttk.LabelFrame(parent, text="设计改进对比", style='Card.TLabelFrame', padding="24")
        comparison_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        comparison_frame.columnconfigure(0, weight=1)
        comparison_frame.columnconfigure(1, weight=1)
        comparison_frame.rowconfigure(1, weight=1)
        
        # 改进前
        before_frame = ttk.LabelFrame(comparison_frame, text="改进前", style='Card.TLabelFrame', padding="16")
        before_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 8))
        
        before_text = tk.Text(before_frame, height=15, wrap=tk.WORD, font=('Segoe UI', 10))
        before_text.pack(fill=tk.BOTH, expand=True)
        before_text.insert(tk.END, """❌ 设计问题：

• 使用了10种不同颜色，缺乏统一性
• 大量emoji图标，显得不够专业
• 传统的tkinter样式，缺乏现代感
• 间距不够统一，视觉层次混乱
• 按钮样式过于传统
• 颜色对比度不够合理
• 缺乏卡片式设计理念
• 字体大小和权重不够统一

具体问题：
- 主蓝色: #2E86C1
- 浅蓝色: #AED6F1  
- 深蓝色: #1B4F72
- 成功绿色: #27AE60
- 错误红色: #E74C3C
- 警告橙色: #F39C12
- 白色: #FFFFFF
- 浅灰色: #F8F9FA
- 边框灰色: #E5E5E5
- 深色文字: #2C3E50

颜色过多，缺乏设计系统""")
        before_text.config(state=tk.DISABLED)
        
        # 改进后
        after_frame = ttk.LabelFrame(comparison_frame, text="改进后", style='Card.TLabelFrame', padding="16")
        after_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(8, 0))
        
        after_text = tk.Text(after_frame, height=15, wrap=tk.WORD, font=('Segoe UI', 10))
        after_text.pack(fill=tk.BOTH, expand=True)
        after_text.insert(tk.END, """✅ 现代化改进：

• 极简颜色方案，只使用4种主要颜色
• 移除emoji，使用简洁文字
• 现代扁平化设计风格
• 统一的间距系统（8px基准）
• 卡片式布局，层次分明
• 优化的颜色对比度
• 现代化字体层级系统
• 一致的交互反馈

新颜色系统：
- 主色调: #2563EB (现代蓝)
- 表面色: #F8FAFC (极浅灰)  
- 背景色: #FFFFFF (纯白)
- 文字色: #1E293B (深灰)
- 次要文字: #64748B (中灰)
- 边框色: #E2E8F0 (浅灰)

设计原则：
• 极简主义 - 减少视觉噪音
• 一致性 - 统一的设计语言
• 可访问性 - 良好的对比度
• 现代感 - 符合当前设计趋势""")
        after_text.config(state=tk.DISABLED)

def main():
    """主函数"""
    root = tk.Tk()
    app = ModernGUIDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main()
