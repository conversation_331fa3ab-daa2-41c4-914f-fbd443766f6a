#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版GUI测试 - 确保所有组件正确显示
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import configparser

class HubStudioGUIFixed:
    def __init__(self, root):
        self.root = root
        self.root.title("HubStudio + YouTube 自动化上传工具 - 修复版")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        self.root.resizable(True, True)
        
        # 设置现代化窗口背景色
        self.root.configure(bg='#F8FAFC')
        
        # 设置现代化样式
        self.setup_modern_styles()
        
        # 初始化变量
        self.config = configparser.ConfigParser()
        self.video_list = []
        self.is_uploading = False
        
        # 创建界面
        self.create_widgets()
    
    def setup_modern_styles(self):
        """设置现代化界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 定义极简颜色方案
        colors = {
            'primary': '#2563EB',           # 主色调 - 现代蓝色
            'surface': '#F8FAFC',           # 表面色 - 极浅灰
            'background': '#FFFFFF',        # 背景色 - 纯白
            'text': '#1E293B',              # 文字色 - 深灰
            'text_secondary': '#64748B',    # 次要文字 - 中灰
            'border': '#E2E8F0',            # 边框色 - 浅灰
            'success': '#10B981',           # 成功色 - 现代绿
            'error': '#EF4444',             # 错误色 - 现代红
            'warning': '#F59E0B'            # 警告色 - 现代橙
        }
        
        # 配置组件样式
        style.configure('TFrame', background=colors['background'])
        
        # 卡片样式
        style.configure('Card.TLabelFrame', 
                       background=colors['background'],
                       borderwidth=1, 
                       relief='solid', 
                       bordercolor=colors['border'])
        style.configure('Card.TLabelFrame.Label', 
                       background=colors['background'],
                       foreground=colors['text'], 
                       font=('Segoe UI', 12, 'bold'))
        
        # 按钮样式
        style.configure('Primary.TButton', 
                       background=colors['primary'],
                       foreground=colors['background'],
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 10, 'bold'),
                       padding=(20, 12))
        style.map('Primary.TButton',
                 background=[('active', '#1D4ED8')])
        
        style.configure('Secondary.TButton', 
                       background=colors['surface'],
                       foreground=colors['text'],
                       borderwidth=1,
                       bordercolor=colors['border'],
                       focuscolor='none',
                       font=('Segoe UI', 10),
                       padding=(16, 10))
        
        # 输入框样式
        style.configure('Modern.TEntry',
                       fieldbackground=colors['background'],
                       borderwidth=2,
                       bordercolor=colors['border'],
                       focuscolor=colors['primary'],
                       font=('Segoe UI', 11),
                       padding=12)
        
        # 文字样式
        style.configure('Title.TLabel', 
                       background=colors['background'],
                       foreground=colors['text'],
                       font=('Segoe UI', 24, 'bold'))
        
        style.configure('Subtitle.TLabel', 
                       background=colors['background'],
                       foreground=colors['text_secondary'],
                       font=('Segoe UI', 14))
        
        style.configure('Subheader.TLabel', 
                       background=colors['background'],
                       foreground=colors['text'],
                       font=('Segoe UI', 12, 'bold'))
        
        style.configure('Body.TLabel', 
                       background=colors['background'],
                       foreground=colors['text_secondary'],
                       font=('Segoe UI', 10))
        
        # 进度条样式
        style.configure('Modern.TProgressbar',
                       background=colors['primary'],
                       troughcolor=colors['surface'],
                       borderwidth=0)
        
        # 分隔线样式
        style.configure('Modern.TSeparator',
                       background=colors['border'])
        
        self.colors = colors
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="32")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 标题栏
        main_frame.rowconfigure(1, weight=0)  # 视频选择栏
        main_frame.rowconfigure(2, weight=1)  # 主要内容区域
        main_frame.rowconfigure(3, weight=0)  # 日志区域
        main_frame.rowconfigure(4, weight=0)  # 状态栏
        
        # 创建各个部分
        self.create_title_bar(main_frame)
        self.create_video_selection_bar(main_frame)
        self.create_content_area(main_frame)
        self.create_log_panel(main_frame)
        self.create_status_bar(main_frame)
    
    def create_title_bar(self, parent):
        """创建标题栏"""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 32))
        title_frame.columnconfigure(0, weight=1)
        
        # 主标题
        title_label = ttk.Label(title_frame, text="HubStudio + YouTube 自动化上传", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # 副标题
        subtitle_label = ttk.Label(title_frame, text="高效、智能的视频批量上传解决方案", style='Subtitle.TLabel')
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(8, 0))
        
        # 分隔线
        separator = ttk.Separator(title_frame, orient='horizontal', style='Modern.TSeparator')
        separator.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(24, 0))
    
    def create_video_selection_bar(self, parent):
        """创建视频选择栏"""
        video_frame = ttk.LabelFrame(parent, text="视频文件", style='Card.TLabelFrame', padding="20")
        video_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 24))
        video_frame.columnconfigure(2, weight=1)
        
        # 选择视频按钮
        select_btn = ttk.Button(video_frame, text="选择视频文件", style='Secondary.TButton')
        select_btn.grid(row=0, column=0, padx=(0, 16))
        
        # 视频状态显示
        self.video_count_var = tk.StringVar(value="未选择视频")
        status_label = ttk.Label(video_frame, textvariable=self.video_count_var, style='Body.TLabel')
        status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 16))
        
        # 视频文件路径显示框
        self.video_path_var = tk.StringVar(value="")
        self.video_path_entry = ttk.Entry(video_frame, textvariable=self.video_path_var, style='Modern.TEntry', state='readonly', width=50)
        self.video_path_entry.grid(row=0, column=2, sticky=(tk.W, tk.E), padx=(0, 16))
        
        # 操作按钮组
        button_frame = ttk.Frame(video_frame)
        button_frame.grid(row=0, column=3)
        
        clear_btn = ttk.Button(button_frame, text="清除", style='Secondary.TButton')
        clear_btn.grid(row=0, column=0, padx=(0, 12))
        
        self.upload_btn = ttk.Button(button_frame, text="开始上传", style='Primary.TButton', state='disabled')
        self.upload_btn.grid(row=0, column=1, padx=(0, 12))
        
        self.stop_btn = ttk.Button(button_frame, text="停止上传", style='Secondary.TButton', state='disabled')
        self.stop_btn.grid(row=0, column=2)
    
    def create_content_area(self, parent):
        """创建主要内容区域"""
        content_frame = ttk.Frame(parent)
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=24)
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧面板 - 视频配置
        self.create_control_panel(content_frame)
        
        # 右侧面板 - HubStudio设置
        self.create_settings_panel(content_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="视频配置", style='Card.TLabelFrame', padding="24")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 16))
        control_frame.columnconfigure(0, weight=1)
        
        # 连接状态
        status_frame = ttk.LabelFrame(control_frame, text="连接状态", style='Card.TLabelFrame', padding="16")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        self.hubstudio_status_var = tk.StringVar(value="正在检测HubStudio环境...")
        status_label = ttk.Label(status_frame, textvariable=self.hubstudio_status_var, style='Body.TLabel')
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 并发设置
        concurrent_frame = ttk.LabelFrame(control_frame, text="并发设置", style='Card.TLabelFrame', padding="16")
        concurrent_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        ttk.Label(concurrent_frame, text="并发数量", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 12))
        
        spinbox_frame = ttk.Frame(concurrent_frame)
        spinbox_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        
        self.concurrent_count_var = tk.IntVar(value=1)
        concurrent_spinbox = ttk.Spinbox(spinbox_frame, from_=1, to=10, textvariable=self.concurrent_count_var, width=8)
        concurrent_spinbox.grid(row=0, column=0, padx=(0, 12))
        
        ttk.Label(spinbox_frame, text="个浏览器环境同时工作", style='Body.TLabel').grid(row=0, column=1, sticky=tk.W)
        
        # 视频信息
        video_info_frame = ttk.LabelFrame(control_frame, text="视频信息", style='Card.TLabelFrame', padding="16")
        video_info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        video_info_frame.columnconfigure(0, weight=1)
        video_info_frame.rowconfigure(3, weight=1)
        
        # 标题
        ttk.Label(video_info_frame, text="标题", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 8))
        self.title_var = tk.StringVar(value="")
        title_entry = ttk.Entry(video_info_frame, textvariable=self.title_var, style='Modern.TEntry')
        title_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 16))
        
        # 描述
        ttk.Label(video_info_frame, text="描述", style='Subheader.TLabel').grid(row=2, column=0, sticky=tk.W, pady=(0, 8))
        
        desc_frame = ttk.Frame(video_info_frame)
        desc_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 16))
        desc_frame.columnconfigure(0, weight=1)
        desc_frame.rowconfigure(0, weight=1)
        
        self.desc_text = tk.Text(desc_frame, height=6, wrap=tk.WORD, font=('Segoe UI', 11),
                                bg=self.colors['background'], fg=self.colors['text'],
                                borderwidth=2, relief='solid')
        self.desc_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        desc_scrollbar = ttk.Scrollbar(desc_frame, orient='vertical', command=self.desc_text.yview)
        desc_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.desc_text.configure(yscrollcommand=desc_scrollbar.set)
    
    def create_settings_panel(self, parent):
        """创建设置面板"""
        settings_frame = ttk.LabelFrame(parent, text="HubStudio 设置", style='Card.TLabelFrame', padding="24")
        settings_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(16, 0))
        settings_frame.columnconfigure(0, weight=1)
        
        # API状态
        api_status_frame = ttk.LabelFrame(settings_frame, text="API 连接状态", style='Card.TLabelFrame', padding="16")
        api_status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 16))
        
        self.api_status_var = tk.StringVar(value="未连接")
        api_status_label = ttk.Label(api_status_frame, textvariable=self.api_status_var, style='Body.TLabel')
        api_status_label.grid(row=0, column=0, sticky=tk.W)
        
        # API配置
        config_frame = ttk.LabelFrame(settings_frame, text="API 配置", style='Card.TLabelFrame', padding="16")
        config_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 16))
        config_frame.columnconfigure(0, weight=1)
        
        # API地址
        ttk.Label(config_frame, text="API 地址", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 8))
        self.api_url_var = tk.StringVar(value="http://localhost:6873")
        api_url_entry = ttk.Entry(config_frame, textvariable=self.api_url_var, style='Modern.TEntry')
        api_url_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        
        # API ID
        ttk.Label(config_frame, text="API ID", style='Subheader.TLabel').grid(row=2, column=0, sticky=tk.W, pady=(0, 8))
        self.api_id_var = tk.StringVar(value="")
        api_id_entry = ttk.Entry(config_frame, textvariable=self.api_id_var, style='Modern.TEntry')
        api_id_entry.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        
        # API Secret
        ttk.Label(config_frame, text="API Secret", style='Subheader.TLabel').grid(row=4, column=0, sticky=tk.W, pady=(0, 8))
        self.api_secret_var = tk.StringVar(value="")
        api_secret_entry = ttk.Entry(config_frame, textvariable=self.api_secret_var, style='Modern.TEntry', show="*")
        api_secret_entry.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 16))
        
        # 操作按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=6, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        
        save_btn = ttk.Button(button_frame, text="保存配置", style='Secondary.TButton')
        save_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8), pady=(0, 12))
        
        test_btn = ttk.Button(button_frame, text="测试连接", style='Primary.TButton')
        test_btn.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(8, 0), pady=(0, 12))
        
        refresh_btn = ttk.Button(button_frame, text="刷新环境", style='Secondary.TButton')
        refresh_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 8))
        
        help_btn = ttk.Button(button_frame, text="使用帮助", style='Secondary.TButton')
        help_btn.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(8, 0))
    
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = ttk.LabelFrame(parent, text="实时日志", style='Card.TLabelFrame', padding="20")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(24, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)
        
        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        log_toolbar.columnconfigure(0, weight=1)
        
        ttk.Label(log_toolbar, text="系统运行日志", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        clear_btn = ttk.Button(log_toolbar, text="清除日志", style='Secondary.TButton')
        clear_btn.grid(row=0, column=1, sticky=tk.E)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 height=8, 
                                                 wrap=tk.WORD,
                                                 font=('Consolas', 10),
                                                 bg=self.colors['surface'],
                                                 fg=self.colors['text'],
                                                 borderwidth=1,
                                                 relief='solid')
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加一些示例日志
        self.log_text.insert(tk.END, "[12:34:56] INFO: 系统启动完成\n")
        self.log_text.insert(tk.END, "[12:34:57] INFO: 正在检测HubStudio环境...\n")
        self.log_text.insert(tk.END, "[12:34:58] WARNING: 请配置API凭证\n")
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.LabelFrame(parent, text="系统状态", style='Card.TLabelFrame', padding="20")
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(24, 0))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态信息
        status_info = ttk.Frame(status_frame)
        status_info.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 12))
        status_info.columnconfigure(1, weight=1)
        
        ttk.Label(status_info, text="当前状态", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.status_var = tk.StringVar(value="系统就绪")
        status_label = ttk.Label(status_info, textvariable=self.status_var, style='Body.TLabel')
        status_label.grid(row=0, column=1, sticky=tk.W, padx=(16, 0))
        
        # 进度条
        progress_frame = ttk.Frame(status_frame)
        progress_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        progress_frame.columnconfigure(1, weight=1)
        
        ttk.Label(progress_frame, text="进度", style='Subheader.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, 
                                      variable=self.progress_var, 
                                      maximum=100,
                                      length=400,
                                      style='Modern.TProgressbar')
        progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(16, 0))

def main():
    """主函数"""
    root = tk.Tk()
    app = HubStudioGUIFixed(root)
    root.mainloop()

if __name__ == "__main__":
    main()
