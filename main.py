"""
HubStudio + YouTube 自动化视频上传主程序
专门用于HubStudio指纹浏览器自动上传视频到YouTube
"""

import os
import sys
import time
import configparser
from pathlib import Path
from loguru import logger
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
# 导入自动化模块
from browser_manager import HubStudioManager
from video_uploader_unified import VideoUploaderUnified
# from video_processor import VideoProcessor  # 极简版不需要


class HubStudioYouTubeAutomation:
    def __init__(self, config_path="config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_path, encoding='utf-8')
        self.setup_logging()
        self.hubstudio_manager = None
        self.video_uploader = None
        # self.video_processor = VideoProcessor(self.config)  # 极简版不需要
        
    def setup_logging(self):
        """设置日志"""
        log_level = self.config.get('LOG', 'log_level', fallback='INFO')
        log_file = self.config.get('LOG', 'log_file', fallback='./logs/automation.log')
        
        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 配置loguru
        logger.remove()
        logger.add(sys.stderr, level=log_level)
        logger.add(log_file, rotation="10 MB", retention="7 days", level=log_level)
        
    def initialize(self, profile_id=None):
        """初始化HubStudio浏览器和YouTube上传器"""
        try:
            # 初始化HubStudio管理器
            self.hubstudio_manager = HubStudioManager(self.config, profile_id)

            # 启动HubStudio浏览器
            driver = self.hubstudio_manager.start_browser_profile()
            if not driver:
                logger.error("HubStudio浏览器启动失败")
                return False

            # 初始化统一视频上传器
            logger.info("初始化统一视频上传器")
            self.video_uploader = VideoUploaderUnified(driver, self.config)

            logger.info("HubStudio + YouTube 自动化系统初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def upload_single_video(self, video_path, title=None, description=None, tags=None, children_content=None):
        """上传单个视频"""
        try:
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return False

            # 极简版：跳过验证，直接使用默认值
            if not title:
                title = os.path.basename(video_path)
            if not description:
                description = f"视频文件: {os.path.basename(video_path)}"
            if not tags:
                tags = self.config.get('VIDEO', 'default_tags', fallback='automation,video,upload')

            # 极简版：跳过视频信息获取
            logger.info(f"视频文件: {os.path.basename(video_path)}")

            logger.info(f"开始上传视频: {video_path}")
            logger.info(f"标题: {title}")
            logger.info(f"描述: {description[:100]}...")

            # 执行统一视频上传
            logger.info("🚀 开始统一上传...")
            success = self.video_uploader.upload_video(video_path, title, description, children_content, tags)

            if success:
                logger.info(f"YouTube视频上传成功: {video_path}")
            else:
                logger.error(f"YouTube视频上传失败: {video_path}")
                # 截图保存错误状态
                if self.config.getboolean('AUTOMATION', 'screenshot_on_error', fallback=True):
                    timestamp = int(time.time())
                    self.video_uploader._take_screenshot(f"error_{timestamp}")

            return success

        except Exception as e:
            logger.error(f"上传视频异常: {e}")
            return False
    
    def upload_batch_videos(self, video_folder=None):
        """批量上传视频"""
        try:
            if not video_folder:
                video_folder = self.config.get('VIDEO', 'video_folder', fallback='./videos')

            if not os.path.exists(video_folder):
                logger.error(f"视频文件夹不存在: {video_folder}")
                return False

            # 极简版：直接扫描文件夹
            video_files = self._scan_video_folder_simple(video_folder)

            if not video_files:
                logger.warning(f"在文件夹中未找到视频文件: {video_folder}")
                return False

            logger.info(f"找到 {len(video_files)} 个视频文件")
            
            # 批量上传
            success_count = 0
            retry_count = int(self.config.get('AUTOMATION', 'retry_count', fallback=3))
            upload_interval = int(self.config.get('AUTOMATION', 'upload_interval', fallback=10))

            for i, video_file in enumerate(video_files):
                video_path = str(video_file)
                video_name = Path(video_file).stem

                logger.info(f"处理视频 {i+1}/{len(video_files)}: {video_name}")
                
                # 尝试上传
                for attempt in range(retry_count):
                    try:
                        logger.info(f"上传视频 ({attempt + 1}/{retry_count}): {video_name}")
                        
                        if self.upload_single_video(video_path, title=video_name):
                            success_count += 1
                            break
                        else:
                            if attempt < retry_count - 1:
                                logger.warning(f"上传失败，等待重试...")
                                time.sleep(10)
                            else:
                                logger.error(f"视频上传最终失败: {video_name}")
                                
                    except Exception as e:
                        logger.error(f"上传视频异常: {e}")
                        if attempt < retry_count - 1:
                            time.sleep(10)
                
                # 上传间隔
                if i < len(video_files) - 1:  # 不是最后一个文件
                    logger.info(f"等待 {upload_interval} 秒后处理下一个视频...")
                    time.sleep(upload_interval)
                
            logger.info(f"批量上传完成: 成功 {success_count}/{len(video_files)}")
            return success_count > 0

        except Exception as e:
            logger.error(f"批量上传异常: {e}")
            return False

    def _scan_video_folder_simple(self, folder_path):
        """极简版文件夹扫描"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
        video_files = []

        for file in os.listdir(folder_path):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(os.path.join(folder_path, file))

        return video_files

    def cleanup(self):
        """清理资源"""
        try:
            logger.info("清理资源...")
            
            if self.video_uploader:
                self.video_uploader.close()
                
            if self.hubstudio_manager:
                self.hubstudio_manager.stop_browser_profile()
                
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


def main():
    """主函数"""
    automation = HubStudioYouTubeAutomation()
    
    try:
        # 初始化
        if not automation.initialize():
            logger.error("初始化失败，程序退出")
            return
            
        # 检查命令行参数
        if len(sys.argv) > 1:
            video_path = sys.argv[1]
            if os.path.isfile(video_path):
                # 上传单个视频
                automation.upload_single_video(video_path)
            elif os.path.isdir(video_path):
                # 批量上传
                automation.upload_batch_videos(video_path)
            else:
                logger.error(f"无效的路径: {video_path}")
        else:
            # 使用配置文件中的默认文件夹
            automation.upload_batch_videos()
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        automation.cleanup()


if __name__ == "__main__":
    main()
