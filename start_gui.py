#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HubStudio + YouTube 自动化上传工具启动器
"""

import sys
import os
from loguru import logger

def main():
    """启动GUI程序"""
    try:
        logger.info("🚀 启动HubStudio + YouTube自动化上传工具")
        
        # 导入GUI主程序
        from gui_main import main as gui_main
        
        # 启动GUI
        gui_main()
        
    except ImportError as e:
        logger.error(f"❌ 导入模块失败: {e}")
        logger.info("请确保所有依赖已正确安装: pip install -r requirements.txt")
        input("按回车键退出...")
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
